import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";

interface AnalyticsQuery {
  eventType?: AnalyticsEventType;
  entityId?: number;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface DashboardStats {
  // Core Business Metrics
  totalRecipes: number;
  activeUsers: number;
  topCategory: {
    name: string;
    count: number;
  };
  highestImpressionRecipe: {
    name: string;
    impressions: number;
  };

  // Recipe Analytics
  totalViews: number;
  totalContactSubmissions: number;

  // Dashboard Charts Data
  recipeViewsTrend: any[]; // Top 10 recipes with views for last 30 days
  categoryPerformance: any[]; // Bar chart data
  userEngagementHeatmap: any[]; // Heatmap data for user engagement
  conversionFunnel: any[]; // Conversion analytics data
  recentActivity: any[];
}

class AnalyticsService {
  /**
   * Track any event with flexible metadata - Supports organization filtering
   */
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    organizationId?: string;
    userId?: number;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    // Remove referrer from metadata if present (for public APIs)
    const cleanMetadata = { ...data.metadata };
    delete cleanMetadata.referrer;

    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      organization_id: data.organizationId,
      user_id: data.userId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: cleanMetadata,
    });
  }

  /**
   * Get analytics data with flexible filtering - Public API
   */
  async getAnalytics(query: AnalyticsQuery): Promise<Analytics[]> {
    const whereClause: any = {};

    if (query.eventType) whereClause.event_type = query.eventType;
    whereClause.entity_type = AnalyticsEntityType.RECIPE; // Always recipe
    if (query.entityId) whereClause.entity_id = query.entityId;

    if (query.startDate && query.endDate) {
      whereClause.created_at = {
        [Op.between]: [query.startDate, query.endDate],
      };
    }

    return await Analytics.findAll({
      where: whereClause,
      limit: query.limit || 100,
      offset: query.offset || 0,
      order: [["created_at", "DESC"]],
    });
  }

  /**
   * Get dashboard statistics - Organization-based and optimized
   */
  async getDashboardStats(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Get all counts in parallel with organization-filtered queries
      const [
        recipeCount,
        activeUsersCount,
        topCategoryData,
        totalViews,
        contactCount,
        recipeViewsTrend,
        categoryPerformance,
        userEngagementHeatmap,
        conversionFunnel,
        recentActivity,
        highestImpressionRecipe,
      ] = await Promise.all([
        this.getRecipeCount(organizationId),
        this.getActiveUsersCount(organizationId, startDate, endDate),
        this.getTopCategory(organizationId, startDate, endDate),
        this.getTotalRecipeViews(organizationId, startDate, endDate),
        this.getContactSubmissions(organizationId, startDate, endDate),
        this.getRecipeViewsTrend(organizationId, startDate, endDate),
        this.getCategoryPerformance(organizationId, startDate, endDate),
        this.getUserEngagementHeatmap(organizationId, startDate, endDate),
        this.getConversionFunnel(organizationId, startDate, endDate),
        this.getRecentActivity(organizationId, 10),
        this.getHighestImpressionRecipe(organizationId),
      ]);

      return {
        // Core Business Metrics
        totalRecipes: recipeCount,
        activeUsers: activeUsersCount,
        topCategory: topCategoryData,
        highestImpressionRecipe: highestImpressionRecipe,

        // Recipe Analytics
        totalViews: totalViews,
        totalContactSubmissions: contactCount,

        // Dashboard Charts Data
        recipeViewsTrend: recipeViewsTrend,
        categoryPerformance: categoryPerformance,
        userEngagementHeatmap: userEngagementHeatmap,
        conversionFunnel: conversionFunnel,
        recentActivity: recentActivity,
      };
    } catch (error) {
      console.error("Dashboard stats error:", error);
      return {
        totalRecipes: 0,
        activeUsers: 0,
        topCategory: { name: "No Data", count: 0 },
        highestImpressionRecipe: { name: "No Data", impressions: 0 },
        totalViews: 0,
        totalContactSubmissions: 0,
        recipeViewsTrend: [],
        categoryPerformance: [],
        userEngagementHeatmap: [],
        conversionFunnel: [],
        recentActivity: [],
      };
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS - Organization-filtered and optimized
  // ============================================================================

  /**
   * Get total recipe count - Organization filtered
   */
  private async getRecipeCount(
    organizationId?: string | null
  ): Promise<number> {
    try {
      const whereClause = organizationId
        ? `WHERE recipe_status != 'deleted' AND organization_id = :organizationId`
        : `WHERE recipe_status != 'deleted'`;

      const result = await sequelize.query(
        `SELECT COUNT(*) as total FROM mo_recipe ${whereClause}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get active users count - Organization filtered
   */
  private async getActiveUsersCount(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get top performing category based on recipe views - Organization filtered
   */
  private async getTopCategory(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<{ name: string; count: number }> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT c.category_name, COUNT(a.id) as count
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         JOIN mo_category c ON r.category_id = c.id
         WHERE a.event_type = 'recipe_view'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         GROUP BY c.category_name
         ORDER BY count DESC
         LIMIT 1`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.category_name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch {
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get total recipe views from analytics - Organization filtered
   */
  private async getTotalRecipeViews(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        event_type: AnalyticsEventType.RECIPE_VIEW,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get contact form submissions count - Organization filtered
   */
  private async getContactSubmissions(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      if (organizationId) {
        whereCondition.organization_id = organizationId;
      }

      const result = await Analytics.count({
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get recipe with highest impressions - Organization filtered
   */
  private async getHighestImpressionRecipe(
    organizationId?: string | null
  ): Promise<{
    name: string;
    impressions: number;
  }> {
    try {
      const orgFilter = organizationId
        ? `WHERE organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT recipe_name, recipe_impression as impressions
         FROM mo_recipe
         ${orgFilter}
         ORDER BY recipe_impression DESC
         LIMIT 1`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const recipe = result[0] as any;
        return {
          name: recipe.recipe_name || "Unknown Recipe",
          impressions: parseInt(recipe.impressions) || 0,
        };
      }

      return { name: "No Data", impressions: 0 };
    } catch {
      return { name: "No Data", impressions: 0 };
    }
  }

  /**
   * Get user engagement heatmap data - Organization filtered dynamic query
   */
  private async getUserEngagementHeatmap(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           EXTRACT(HOUR FROM created_at) as hour,
           EXTRACT(DOW FROM created_at) as day_of_week,
           COUNT(*) as engagement_count,
           COUNT(DISTINCT user_id) as unique_users,
           ROUND(COUNT(*) * 100.0 / NULLIF(COUNT(DISTINCT user_id), 0), 2) as view_percentage,
           ROUND(COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as contact_percentage
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         GROUP BY EXTRACT(HOUR FROM created_at), EXTRACT(DOW FROM created_at)
         ORDER BY day_of_week, hour`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        hour: parseInt(row.hour) || 0,
        day_of_week: parseInt(row.day_of_week) || 0,
        engagement_count: parseInt(row.engagement_count) || 0,
        unique_users: parseInt(row.unique_users) || 0,
        view_percentage: parseFloat(row.view_percentage) || 0,
        contact_percentage: parseFloat(row.contact_percentage) || 0,
        intensity: Math.min(parseInt(row.engagement_count) / 10, 1), // Normalized intensity for heatmap
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get conversion funnel data - Organization filtered dynamic query
   */
  private async getConversionFunnel(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           'Recipe Views' as stage,
           1 as stage_order,
           COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         UNION ALL

         SELECT
           'CTA Clicks' as stage,
           2 as stage_order,
           COUNT(CASE WHEN event_type = 'cta_click' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         UNION ALL

         SELECT
           'Contact Forms' as stage,
           3 as stage_order,
           COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) as stage_count,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate ${orgFilter}

         ORDER BY stage_order`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      // Calculate conversion rates
      const totalViews =
        result.find((r: any) => r.stage === "Recipe Views")?.stage_count || 1;

      return result.map((row: any) => {
        const stageCount = parseInt(row.stage_count) || 0;
        const conversionRate =
          totalViews > 0 ? (stageCount / totalViews) * 100 : 0;

        return {
          stage: row.stage,
          stage_order: parseInt(row.stage_order),
          count: stageCount,
          unique_users: parseInt(row.unique_users) || 0,
          conversion_rate: Math.round(conversionRate * 100) / 100,
          drop_off_rate: Math.round((100 - conversionRate) * 100) / 100,
        };
      });
    } catch {
      return [
        {
          stage: "Recipe Views",
          stage_order: 1,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "CTA Clicks",
          stage_order: 2,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "Contact Forms",
          stage_order: 3,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
      ];
    }
  }

  /**
   * Get recipe views trend - Organization filtered, Top 10 recipes with single recipe name
   */
  private async getRecipeViewsTrend(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id as recipe_id,
           r.recipe_name,
           COUNT(a.id) as total_views,
           COUNT(CASE WHEN a.created_at >= :startDate THEN 1 END) as recent_views,
           (COUNT(a.id) + COALESCE(r.recipe_impression, 0)) as combined_views
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
         WHERE r.recipe_status != 'deleted' ${orgFilter}
         GROUP BY r.id, r.recipe_name, r.recipe_impression
         ORDER BY combined_views DESC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        recipe_id: parseInt(row.recipe_id),
        recipe_name: row.recipe_name || `Recipe ${row.recipe_id}`, // Single name, not array
        total_views: parseInt(row.total_views) || 0,
        recent_views: parseInt(row.recent_views) || 0,
        combined_views: parseInt(row.combined_views) || 0,
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get category performance data - Organization filtered
   */
  private async getCategoryPerformance(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           c.category_name,
           COUNT(DISTINCT r.id) as unique_recipes,
           COUNT(a.id) as total_views,
           COUNT(DISTINCT a.user_id) as unique_users,
           ROUND(COUNT(a.id) * 100.0 / NULLIF(COUNT(DISTINCT r.id), 0), 2) as avg_views_per_recipe
         FROM mo_category c
         LEFT JOIN mo_recipe r ON c.id = r.category_id
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
         WHERE c.category_status = 'active' ${orgFilter}
         GROUP BY c.id, c.category_name
         HAVING COUNT(DISTINCT r.id) > 0
         ORDER BY total_views DESC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        category_name: row.category_name || "Unknown",
        unique_recipes: parseInt(row.unique_recipes) || 0,
        total_views: parseInt(row.total_views) || 0,
        unique_users: parseInt(row.unique_users) || 0,
        avg_views_per_recipe:
          Math.round(
            (parseInt(row.total_views) /
              Math.max(parseInt(row.unique_recipes), 1)) *
              100
          ) / 100,
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get recent activity data - Organization filtered
   */
  private async getRecentActivity(
    organizationId: string | null | undefined,
    limit: number = 10
  ): Promise<any[]> {
    try {
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           a.event_type,
           a.entity_id as recipe_id,
           a.created_at,
           a.user_id,
           r.recipe_name,
           COALESCE(r.recipe_name, CONCAT('Recipe ', a.entity_id)) as actual_recipe_name
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.entity_type = 'recipe' ${orgFilter}
         ORDER BY a.created_at DESC
         LIMIT :limit`,
        {
          replacements: { organizationId, limit },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        event_type: row.event_type,
        recipe_id: parseInt(row.recipe_id),
        recipe_name: row.actual_recipe_name || `Recipe ${row.recipe_id}`,
        created_at: row.created_at,
        user_id: row.user_id,
        activity_description: this.getActivityDescription(
          row.event_type,
          row.actual_recipe_name || row.recipe_name
        ),
      }));
    } catch {
      return [];
    }
  }

  /**
   * Helper method to generate activity descriptions
   */
  private getActivityDescription(
    eventType: string,
    recipeName: string
  ): string {
    switch (eventType) {
      case "recipe_view":
        return `Viewed "${recipeName}"`;
      case "cta_click":
        return `Clicked CTA on "${recipeName}"`;
      case "contact_form_submit":
        return `Submitted contact form for "${recipeName}"`;
      default:
        return `Activity on "${recipeName}"`;
    }
  }

  /**
   * Helper method to parse date ranges
   */
  private getDateRange(dateRange: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (dateRange) {
      case "last_7_days":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "last_30_days":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "last_90_days":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "last_year":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    return { startDate, endDate };
  }

  // ============================================================================
  // ANALYTICS CONTROLLER METHODS - Organization-filtered and dynamic
  // ============================================================================

  /**
   * Get CTA click analytics - Enhanced with search, cta_type filter, and date ranges
   */
  async getCtaClickAnalytics(
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 50,
    ctaType?: string,
    search?: string, // General search term for recipe name, user info, etc.
    sort_order: string = "created_at:desc",
    sort_by?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      const offset = (page - 1) * limit;

      // CTA Type filter
      const ctaFilter = ctaType ? `AND a.metadata->>'cta_type' = :ctaType` : "";

      // Enhanced search filter - searches in recipe name, user info, and metadata
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_name ILIKE :search
          OR a.metadata->>'user_name' ILIKE :search
          OR a.metadata->>'user_email' ILIKE :search
          OR a.metadata->>'cta_type' ILIKE :search
        )`;
      }

      // Parse sort parameter
      const [sortField, sortOrder] = sort_order.split(":");
      const validSortFields = [
        "created_at",
        "recipe_name",
        "cta_type",
        "user_name",
      ];
      const finalSortField =
        sort_by ||
        (validSortFields.includes(sortField) ? sortField : "created_at");
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      // Count query
      const countResult = await sequelize.query(
        `SELECT COUNT(*) as total
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${ctaFilter}
         ${searchFilter}`,
        {
          replacements: {
            startDate: finalStartDate,
            endDate: finalEndDate,
            ctaType,
            search: search ? `%${search}%` : undefined,
          },
          type: QueryTypes.SELECT,
        }
      );

      // Data query
      const dataResult = await sequelize.query(
        `SELECT
           a.id,
           a.created_at,
           a.metadata->>'cta_type' as cta_type,
           a.metadata->>'user_name' as user_name,
           a.metadata->>'user_email' as user_email,
           r.recipe_name,
           r.id as recipe_id,
           a.user_id,
           a.ip_address
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${ctaFilter}
         ${searchFilter}
         ORDER BY ${finalSortField === "cta_type" ? "a.metadata->>'cta_type'" : finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "user_name" ? "a.metadata->>'user_name'" : "a." + finalSortField} ${finalSortOrder}
         LIMIT :limit OFFSET :offset`,
        {
          replacements: {
            startDate: finalStartDate,
            endDate: finalEndDate,
            ctaType,
            search: search ? `%${search}%` : undefined,
            limit,
            offset,
          },
          type: QueryTypes.SELECT,
        }
      );

      const total = (countResult[0] as any)?.total || 0;
      const totalPages = Math.ceil(total / limit);

      return {
        status: true,
        data: dataResult,
        pagination: {
          current_page: page,
          page_size: limit,
          total_records: total,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        },
      };
    } catch {
      return {
        status: false,
        data: [],
        pagination: {
          current_page: page,
          page_size: limit,
          total_records: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false,
        },
      };
    }
  }

  /**
   * Get contact submission analytics - Enhanced with recipe name search, user email search, and date ranges
   */
  async getContactSubmissionAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    search?: string, // General search for recipe name, user email, user name
    sort_order: string = "created_at:desc",
    sort_by?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      // Organization filter
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      // Enhanced search filter - searches in recipe name, user email, user name, and contact details
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_name ILIKE :search
          OR a.metadata->>'user_email' ILIKE :search
          OR a.metadata->>'user_name' ILIKE :search
          OR a.metadata->>'contact_email' ILIKE :search
          OR a.metadata->>'contact_name' ILIKE :search
          OR a.metadata->>'message' ILIKE :search
        )`;
      }

      // Parse sort parameter
      const [sortField, sortOrder] = sort_order.split(":");
      const validSortFields = [
        "created_at",
        "recipe_name",
        "user_email",
        "contact_email",
      ];
      const finalSortField =
        sort_by ||
        (validSortFields.includes(sortField) ? sortField : "created_at");
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      let query = `
        SELECT
          a.id,
          a.created_at,
          a.metadata,
          a.metadata->>'user_email' as user_email,
          a.metadata->>'user_name' as user_name,
          a.metadata->>'contact_email' as contact_email,
          a.metadata->>'contact_name' as contact_name,
          a.metadata->>'message' as message,
          r.recipe_name,
          r.id as recipe_id,
          a.user_id,
          a.ip_address
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'contact_form_submit'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${searchFilter}
        ORDER BY ${finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "user_email" ? "a.metadata->>'user_email'" : finalSortField === "contact_email" ? "a.metadata->>'contact_email'" : "a." + finalSortField} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        search: search ? `%${search}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;

        // Get total count for pagination
        const countResult = await sequelize.query(
          `SELECT COUNT(*) as total
           FROM mo_recipe_analytics a
           LEFT JOIN mo_recipe r ON a.entity_id = r.id
           WHERE a.event_type = 'contact_form_submit'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${orgFilter}
           ${searchFilter}`,
          {
            replacements: {
              startDate: finalStartDate,
              endDate: finalEndDate,
              organizationId,
              search: search ? `%${search}%` : undefined,
            },
            type: QueryTypes.SELECT,
          }
        );

        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        const total = (countResult[0] as any)?.total || 0;
        const totalPages = Math.ceil(total / limit);

        return {
          status: true,
          data: dataResult,
          pagination: {
            current_page: page,
            page_size: limit,
            total_records: total,
            total_pages: totalPages,
            has_next: page < totalPages,
            has_prev: page > 1,
          },
        };
      } else {
        // No pagination - return all results
        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        return {
          status: true,
          data: dataResult,
          total: dataResult.length,
        };
      }
    } catch {
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get recipe view analytics - Organization filtered with pagination
   */
  async getRecipeViewAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    recipeName?: string,
    sort: string = "created_at:desc"
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        this.getDateRange(dateRange);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";
      const recipeFilter = recipeName
        ? `AND r.recipe_name ILIKE :recipeName`
        : "";

      // Parse sort parameter
      const [sortField, sortOrder] = sort.split(":");
      const validSortFields = ["created_at", "recipe_name", "view_count"];
      const finalSortField = validSortFields.includes(sortField)
        ? sortField
        : "created_at";
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      let query = `
        SELECT
          r.id as recipe_id,
          r.recipe_name,
          COUNT(a.id) as view_count,
          MAX(a.created_at) as last_viewed
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'recipe_view'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${recipeFilter}
        GROUP BY r.id, r.recipe_name
        ORDER BY ${finalSortField === "recipe_name" ? "r.recipe_name" : finalSortField === "view_count" ? "COUNT(a.id)" : "MAX(a.created_at)"} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        recipeName: recipeName ? `%${recipeName}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;
      }

      const dataResult = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return {
        status: true,
        data: dataResult,
        total: dataResult.length,
      };
    } catch {
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get analytics summary - Organization filtered
   */
  async getAnalyticsSummary(options: {
    organizationId: string | null | undefined;
    page?: number;
    limit?: number;
    dateRange?: string;
    event_type?: string;
    entity_type?: string;
    entity_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      const { organizationId, dateRange = "last_30_days" } = options;
      const { startDate, endDate } = this.getDateRange(dateRange);

      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      // Get summary statistics
      const summaryResult = await sequelize.query(
        `SELECT
           COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as total_views,
           COUNT(CASE WHEN event_type = 'cta_click' THEN 1 END) as total_cta_clicks,
           COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) as total_contacts,
           COUNT(DISTINCT entity_id) as unique_recipes,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate
         ${orgFilter}`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        status: true,
        data: summaryResult[0] || {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    } catch {
      return {
        status: false,
        data: {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    }
  }

  /**
<<<<<<< HEAD
   * Get active users count (users who performed any action in date range)
   */
  private async safeGetActiveUsersCount(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      const result = await Analytics.count({
        distinct: true,
        col: "user_id",
        where: {
          ...(organizationId && { organization_id: organizationId }),
          user_id: { [Op.not]: null },
          ...(startDate &&
            endDate && {
            created_at: {
              [Op.between]: [startDate, endDate],
            },
          }),
        },
      });
      return result || 0;
    } catch (error) {
      console.error("Error getting active users count:", error);
      return 0;
    }
  }

  /**
   * Get top performing category
   */
  private async safeGetTopCategory(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<{ name: string; count: number }> {
    try {
      // Create fallback categories based on recipe IDs in analytics
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as name,
          COUNT(*) as count
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        ORDER BY count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      console.error("Error getting top category:", error);
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get recipe views trend data for line chart
   */
  private async safeGetRecipeViewsTrend(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          DATE(created_at) as date,
          COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as views,
          COUNT(CASE WHEN event_type = 'recipe_bookmark' THEN 1 END) as likes
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY DATE(created_at)
        ORDER BY date ASC
        LIMIT 30
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        date: row.date,
        views: parseInt(row.views) || 0,
        likes: parseInt(row.likes) || 0,
      }));
    } catch (error) {
      console.error("Error getting recipe views trend:", error);
      return [];
    }
  }

  /**
   * Get category performance data for bar chart
   */
  private async safeGetCategoryPerformance(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      // Use analytics data to build category performance with fallback categories
      const query = `
        SELECT 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END as category,
          COUNT(DISTINCT a.entity_id) as recipes,
          COUNT(*) as views
        FROM mo_recipe_analytics a
        WHERE a.event_type = 'recipe_view'
          ${organizationId ? "AND a.organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY 
          CASE 
            WHEN a.entity_id = 1 THEN 'Desserts'
            WHEN a.entity_id = 2 THEN 'Main Course'
            WHEN a.entity_id = 3 THEN 'Appetizers'
            WHEN a.entity_id = 4 THEN 'Desserts'
            WHEN a.entity_id = 5 THEN 'Beverages'
            ELSE 'Other'
          END
        HAVING COUNT(*) > 0
        ORDER BY views DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        category: row.category,
        recipes: parseInt(row.recipes) || 0,
        views: parseInt(row.views) || 0,
      }));
    } catch (error) {
      console.error("Error getting category performance:", error);
      return [];
    }
  }

  /**
   * Get user engagement heatmap data
   */
  private async safeGetUserEngagementHeatmap(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const query = `
        SELECT 
          HOUR(created_at) as hour,
          DAYOFWEEK(created_at) as day_of_week,
          COUNT(*) as engagement_count
        FROM mo_recipe_analytics
        WHERE 1=1
          ${organizationId ? "AND organization_id = :organizationId" : ""}
          ${startDate && endDate ? "AND created_at BETWEEN :startDate AND :endDate" : ""}
        GROUP BY HOUR(created_at), DAYOFWEEK(created_at)
        ORDER BY day_of_week, hour
      `;

      const result = await sequelize.query(query, {
        replacements: { organizationId, startDate, endDate },
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        hour: parseInt(row.hour),
        day: parseInt(row.day_of_week),
        count: parseInt(row.engagement_count) || 0,
      }));
    } catch (error) {
      console.error("Error getting user engagement heatmap:", error);
      return [];
    }
  }

  /**
   * Get conversion funnel data
   */
  private async safeGetConversionFunnel(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<any[]> {
    try {
      const [visitors, recipeViews, bookmarks, shares, subscriptions] =
        await Promise.all([
          Analytics.count({
            distinct: true,
            col: "session_id",
            where: {
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                created_at: { [Op.between]: [startDate, endDate] },
              }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_VIEW,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                created_at: { [Op.between]: [startDate, endDate] },
              }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_BOOKMARK,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                created_at: { [Op.between]: [startDate, endDate] },
              }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.RECIPE_SHARE,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                created_at: { [Op.between]: [startDate, endDate] },
              }),
            },
          }),
          Analytics.count({
            where: {
              event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
              ...(organizationId && { organization_id: organizationId }),
              ...(startDate &&
                endDate && {
                created_at: { [Op.between]: [startDate, endDate] },
              }),
            },
          }),
        ]);

      const maxValue =
        Math.max(visitors, recipeViews, bookmarks, shares, subscriptions) || 1;

      return [
        { stage: "Visitors", count: visitors, percentage: 100 },
        {
          stage: "Recipe Views",
          count: recipeViews,
          percentage: Math.round((recipeViews / maxValue) * 100),
        },
        {
          stage: "Bookmarks",
          count: bookmarks,
          percentage: Math.round((bookmarks / maxValue) * 100),
        },
        {
          stage: "Shares",
          count: shares,
          percentage: Math.round((shares / maxValue) * 100),
        },
        {
          stage: "Subscriptions",
          count: subscriptions,
          percentage: Math.round((subscriptions / maxValue) * 100),
        },
      ];
    } catch (error) {
      console.error("Error getting conversion funnel:", error);
      return [];
    }
  }



  /**
   * Calculate monthly revenue (placeholder - implement based on your business model)
   */
  private async calculateMonthlyRevenue(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<number> {
    try {
      // This is a placeholder implementation
      // You should implement this based on your actual revenue model
      // For example: subscription fees, premium features, etc.

      const contactSubmissions = await Analytics.count({
        where: {
          event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
          ...(organizationId && { organization_id: organizationId }),
          ...(startDate &&
            endDate && {
            created_at: { [Op.between]: [startDate, endDate] },
          }),
        },
      });

      // Example: Estimate $50 revenue per contact form submission
      return contactSubmissions * 50;
    } catch (error) {
      console.error("Error calculating monthly revenue:", error);
      return 0;
    }
  }

  // ============================================================================
  // REAL DATABASE QUERY METHODS - NO FALLBACK DATA
  // ============================================================================

  /**
   * Get real recipe count with category filtering - ENHANCED VERSION
   */
  private async getRealRecipeCount(organizationId?: string, categoryType?: string): Promise<number> {
    try {
      let query = `
        SELECT COUNT(DISTINCT r.id) as total
        FROM mo_recipe r
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id AND rc.status = 'active'
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE r.recipe_status != 'deleted'
      `;

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        // Enhanced category matching - support both exact match and LIKE match
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      const count = parseInt((result[0] as any)?.total || "0");
      return count;
    } catch (error) {
      console.error("Error getting real recipe count:", error);
      return 0;
    }
  }

  /**
   * Get real active users count
   */
  private async getRealActiveUsersCount(organizationId?: string, startDate?: Date, endDate?: Date): Promise<number> {
    try {
      let query = `
        SELECT COUNT(DISTINCT user_id) as total
        FROM mo_recipe_analytics
        WHERE user_id IS NOT NULL
      `;

      const replacements: any = {};

      if (organizationId) {
        query += ` AND organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return parseInt((result[0] as any)?.total || "0");
    } catch (error) {
      console.error("Error getting real active users count:", error);
      return 0;
    }
  }

  /**
   * Get real top category with filtering - ENHANCED VERSION
   */
  private async getRealTopCategory(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<{ name: string; count: number }> {
    try {
      let query = `
        SELECT
          c.category_name as name,
          COUNT(DISTINCT r.id) as recipe_count,
          COALESCE(SUM(r.recipe_impression), 0) as total_impressions,
          COALESCE(analytics_data.analytics_views, 0) as analytics_views,
          (COALESCE(SUM(r.recipe_impression), 0) + COALESCE(analytics_data.analytics_views, 0)) as total_views
        FROM mo_category c
        INNER JOIN mo_recipe_category rc ON c.id = rc.category_id
        INNER JOIN mo_recipe r ON rc.recipe_id = r.id
        LEFT JOIN (
          SELECT
            c2.id as category_id,
            COUNT(*) as analytics_views
          FROM mo_recipe_analytics a
          INNER JOIN mo_recipe r2 ON a.entity_id = r2.id
          INNER JOIN mo_recipe_category rc2 ON r2.id = rc2.recipe_id
          INNER JOIN mo_category c2 ON rc2.category_id = c2.id
          WHERE a.event_type = :eventType
            ${organizationId ? "AND a.organization_id = :organizationId" : ""}
            ${startDate && endDate ? "AND a.created_at BETWEEN :startDate AND :endDate" : ""}
          GROUP BY c2.id
        ) analytics_data ON c.id = analytics_data.category_id
        WHERE c.category_status = 'active'
          AND c.category_type = 'recipe'
          AND r.recipe_status != 'deleted'
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike)`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      if (startDate && endDate) {
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      query += `
        GROUP BY c.id, c.category_name, analytics_data.analytics_views
        ORDER BY total_views DESC, recipe_count DESC
        LIMIT 1
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      const topCategory = result[0] as any;
      if (topCategory) {
        return {
          name: topCategory.name,
          count: parseInt(topCategory.total_views) || parseInt(topCategory.recipe_count) || 0
        };
      }

      return { name: "No Data", count: 0 };
    } catch (error) {
      console.error("Error getting real top category:", error);
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get real total recipe views with category filtering
   */
  private async getRealTotalRecipeViews(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<{ totalViews: number }> {
    try {
      let query = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics a
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return { totalViews: parseInt((result[0] as any)?.total || "0") };
    } catch (error) {
      console.error("Error getting real total recipe views:", error);
      return { totalViews: 0 };
    }
  }

  /**
   * Get real analytics count with category filtering
   */
  private async getRealAnalyticsCount(
    eventType: AnalyticsEventType,
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<number> {
    try {
      let query = `
        SELECT COUNT(*) as total
        FROM mo_recipe_analytics a
      `;

      const replacements: any = { eventType };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return parseInt((result[0] as any)?.total || "0");
    } catch (error) {
      console.error(`Error getting real analytics count for ${eventType}:`, error);
      return 0;
    }
  }

  /**
   * Get real recipe views trend with category filtering and recipe names
   */
  private async getRealRecipeViewsTrend(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          DATE(a.created_at) as date,
          COUNT(*) as views,
          GROUP_CONCAT(DISTINCT r.recipe_title ORDER BY r.recipe_title SEPARATOR ', ') as recipe_names,
          COUNT(DISTINCT a.entity_id) as unique_recipes
        FROM mo_recipe_analytics a
        INNER JOIN mo_recipe r ON a.entity_id = r.id
      `;

      const replacements: any = {
        eventType: AnalyticsEventType.RECIPE_VIEW,
      };

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type = :eventType
          AND r.recipe_status != 'deleted'
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY DATE(a.created_at)
        ORDER BY date ASC
        LIMIT 30
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        date: row.date,
        views: parseInt(row.views),
        unique_recipes: parseInt(row.unique_recipes),
        recipe_names: row.recipe_names ? row.recipe_names.split(', ').slice(0, 5) : [], // Limit to top 5 recipe names
        top_recipes: row.recipe_names ? row.recipe_names.split(', ').slice(0, 3).join(', ') : 'No recipes'
      }));
    } catch (error) {
      console.error("Error getting real recipe views trend:", error);
      return [];
    }
  }

  /**
   * Get real category performance with filtering - ENHANCED VERSION
   */
  private async getRealCategoryPerformance(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      // First, get all categories with their recipe counts
      let query = `
        SELECT
          c.category_name as category,
          COUNT(DISTINCT r.id) as recipes,
          COALESCE(SUM(r.recipe_impression), 0) as total_impressions
        FROM mo_category c
        INNER JOIN mo_recipe_category rc ON c.id = rc.category_id
        INNER JOIN mo_recipe r ON rc.recipe_id = r.id
        WHERE c.category_status = 'active'
          AND c.category_type = 'recipe'
          AND r.recipe_status != 'deleted'
      `;

      const replacements: any = {};

      if (organizationId) {
        query += ` AND r.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike)`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY c.id, c.category_name
        ORDER BY total_impressions DESC, recipes DESC
        LIMIT 10
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      // If no categories found, return empty array
      if (!result || result.length === 0) {
        return [];
      }

      // Now get analytics views for each category
      const categoryPerformance = [];
      for (const row of result as any[]) {
        let analyticsQuery = `
          SELECT COUNT(*) as analytics_views
          FROM mo_recipe_analytics a
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
          WHERE a.event_type = :eventType
            AND c.category_name = :categoryName
            AND c.category_status = 'active'
            AND r.recipe_status != 'deleted'
        `;

        const analyticsReplacements: any = {
          eventType: AnalyticsEventType.RECIPE_VIEW,
          categoryName: row.category,
        };

        if (organizationId) {
          analyticsQuery += ` AND a.organization_id = :organizationId`;
          analyticsReplacements.organizationId = organizationId;
        }

        if (startDate && endDate) {
          analyticsQuery += ` AND a.created_at BETWEEN :startDate AND :endDate`;
          analyticsReplacements.startDate = startDate;
          analyticsReplacements.endDate = endDate;
        }

        const analyticsResult = await sequelize.query(analyticsQuery, {
          replacements: analyticsReplacements,
          type: QueryTypes.SELECT,
        });

        const analyticsViews = parseInt((analyticsResult[0] as any)?.analytics_views || "0");
        const impressionViews = parseInt(row.total_impressions);
        const totalViews = impressionViews + analyticsViews;

        // Enhanced category performance with better data
        categoryPerformance.push({
          category: row.category,
          views: totalViews,
          analytics_views: analyticsViews,
          impression_views: impressionViews,
          recipes: parseInt(row.recipes),
          // Add performance metrics
          avg_views_per_recipe: totalViews > 0 ? (totalViews / parseInt(row.recipes)).toFixed(1) : "0",
          performance_score: this.calculateCategoryPerformanceScore(totalViews, parseInt(row.recipes)),
        });
      }

      return categoryPerformance.sort((a, b) => b.views - a.views);
    } catch (error) {
      console.error("Error getting real category performance:", error);
      return [];
    }
  }

  /**
   * Get real user engagement heatmap
   */
  private async getRealUserEngagementHeatmap(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          HOUR(a.created_at) as hour,
          DAYOFWEEK(a.created_at) as day_of_week,
          COUNT(*) as engagement_count
        FROM mo_recipe_analytics a
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe r ON a.entity_id = r.id
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE a.event_type IN (:eventTypes)
      `;

      replacements.eventTypes = [
        AnalyticsEventType.RECIPE_VIEW,
        AnalyticsEventType.CTA_CLICK,
        AnalyticsEventType.RECIPE_BOOKMARK,
        AnalyticsEventType.RECIPE_SHARE
      ];

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (startDate && endDate) {
        query += ` AND a.created_at BETWEEN :startDate AND :endDate`;
        replacements.startDate = startDate;
        replacements.endDate = endDate;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        GROUP BY HOUR(a.created_at), DAYOFWEEK(a.created_at)
        ORDER BY day_of_week, hour
      `;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => ({
        hour: parseInt(row.hour),
        day_of_week: parseInt(row.day_of_week),
        engagement_count: parseInt(row.engagement_count),
      }));
    } catch (error) {
      console.error("Error getting real user engagement heatmap:", error);
      return [];
    }
  }

  /**
   * Get real conversion funnel
   */
  private async getRealConversionFunnel(
    organizationId?: string,
    startDate?: Date,
    endDate?: Date,
    categoryType?: string
  ): Promise<any[]> {
    try {
      const steps = [
        { name: 'Recipe Views', event_type: AnalyticsEventType.RECIPE_VIEW },
        { name: 'CTA Clicks', event_type: AnalyticsEventType.CTA_CLICK },
        { name: 'Contact Submissions', event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT },
      ];

      const funnelData: Array<{ step: string; count: number; conversion_rate: string }> = [];

      for (const step of steps) {
        const count = await this.getRealAnalyticsCount(
          step.event_type,
          organizationId,
          startDate,
          endDate,
          categoryType
        );

        funnelData.push({
          step: step.name,
          count: count,
          conversion_rate: funnelData.length > 0 && funnelData[0].count > 0 ?
            (count / funnelData[0].count * 100).toFixed(2) :
            (funnelData.length === 0 ? '100.00' : '0.00')
        });
      }

      return funnelData;
    } catch (error) {
      console.error("Error getting real conversion funnel:", error);
      return [];
    }
  }

  /**
   * Get real recent activity
   */
  private async getRealRecentActivity(
    organizationId?: string,
    limit: number = 10,
    categoryType?: string
  ): Promise<any[]> {
    try {
      let query = `
        SELECT
          a.event_type,
          a.entity_id,
          a.created_at,
          a.metadata,
          r.recipe_title
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
      `;

      const replacements: any = {};

      if (categoryType) {
        query += `
          INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id
          INNER JOIN mo_category c ON rc.category_id = c.id
        `;
      }

      query += `
        WHERE 1=1
      `;

      if (organizationId) {
        query += ` AND a.organization_id = :organizationId`;
        replacements.organizationId = organizationId;
      }

      if (categoryType) {
        query += ` AND (c.category_name = :categoryType OR c.category_name LIKE :categoryTypeLike) AND c.category_type = 'recipe' AND c.category_status = 'active'`;
        replacements.categoryType = categoryType;
        replacements.categoryTypeLike = `%${categoryType}%`;
      }

      query += `
        ORDER BY a.created_at DESC
        LIMIT :limit
      `;

      replacements.limit = limit;

      const result = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return result.map((row: any) => {
        const metadata = row.metadata ? JSON.parse(row.metadata) : {};

        return {
          event_type: row.event_type,
          entity_id: row.entity_id,
          recipe_title: row.recipe_title || `Recipe ${row.entity_id}`,
          created_at: row.created_at,
          metadata,
          // Enhanced activity description
          activity_description: this.generateActivityDescription(row.event_type, metadata),
          time_ago: this.getTimeAgo(new Date(row.created_at)),
        };
      });
    } catch (error) {
      console.error("Error getting real recent activity:", error);
      return [];
    }
  }

  /**
   * Calculate growth rate with category filtering
   */
  private async calculateGrowthRate(
    organizationId?: string,
    dateRange: string = "last_30_days",
    categoryType?: string
  ): Promise<number> {
    try {
      const { startDate, endDate } = this.getDateRange(dateRange);

      // Calculate previous period
      const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const previousStartDate = new Date(startDate);
      previousStartDate.setDate(previousStartDate.getDate() - periodDays);
      const previousEndDate = new Date(startDate);

      const [currentViews, previousViews] = await Promise.all([
        this.getRealTotalRecipeViews(organizationId, startDate, endDate, categoryType),
        this.getRealTotalRecipeViews(organizationId, previousStartDate, previousEndDate, categoryType),
      ]);

      if (previousViews.totalViews === 0) return 0;

      return ((currentViews.totalViews - previousViews.totalViews) / previousViews.totalViews) * 100;
    } catch (error) {
      console.error("Error calculating growth rate:", error);
      return 0;
    }
  }

  /**
   * Calculate category performance score (0-100)
   */
  private calculateCategoryPerformanceScore(totalViews: number, recipeCount: number): number {
    if (recipeCount === 0) return 0;

    const avgViewsPerRecipe = totalViews / recipeCount;

    // Performance scoring logic
    if (avgViewsPerRecipe >= 100) return 100;
    if (avgViewsPerRecipe >= 50) return 85;
    if (avgViewsPerRecipe >= 25) return 70;
    if (avgViewsPerRecipe >= 10) return 55;
    if (avgViewsPerRecipe >= 5) return 40;
    if (avgViewsPerRecipe >= 1) return 25;

    return Math.min(Math.round(avgViewsPerRecipe * 25), 100);
  }

  /**
   * Generate human-readable activity description
   */
  private generateActivityDescription(eventType: string, metadata: any): string {
    switch (eventType) {
      case 'recipe_view':
        return `Viewed recipe "${metadata.recipe_name || 'Unknown'}"`;
      case 'cta_click':
        return `Clicked "${metadata.cta_text || metadata.cta_type || 'CTA'}" button`;
      case 'contact_form_submit':
        return `Submitted contact form for "${metadata.recipe_name || 'Unknown'}"`;
      case 'recipe_bookmark':
        return `Bookmarked recipe "${metadata.recipe_name || 'Unknown'}"`;
      case 'recipe_share':
        return `Shared recipe "${metadata.recipe_name || 'Unknown'}" on ${metadata.share_platform || 'social media'}`;
      default:
        return `Performed ${eventType.replace('_', ' ')}`;
    }
  }

  /**
   * Get human-readable time ago string
   */
  private getTimeAgo(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;

    return date.toLocaleDateString();
  }

  /**
   * Generate dashboard insights based on data
   */
  private generateDashboardInsights(data: any): string[] {
    const insights: string[] = [];

    // Recipe performance insights
    if (data.totalRecipes > 0) {
      const avgViews = data.totalViews / data.totalRecipes;
      if (avgViews > 50) {
        insights.push(`🚀 Excellent performance! Your recipes average ${avgViews.toFixed(1)} views each.`);
      } else if (avgViews > 10) {
        insights.push(`📈 Good engagement! Consider promoting recipes with lower views.`);
      } else {
        insights.push(`💡 Opportunity: Focus on SEO and social media promotion to increase visibility.`);
      }
    }

    // Conversion insights
    const conversionRate = data.totalViews > 0 ? (data.contactCount / data.totalViews) * 100 : 0;
    if (conversionRate > 5) {
      insights.push(`🎯 High conversion rate of ${conversionRate.toFixed(1)}%! Your CTAs are working well.`);
    } else if (conversionRate > 2) {
      insights.push(`📊 Decent conversion rate. Consider A/B testing different CTA styles.`);
    } else {
      insights.push(`🔧 Low conversion rate. Review your CTA placement and messaging.`);
    }

    // Growth insights
    if (data.growthRate > 10) {
      insights.push(`📈 Amazing growth of ${data.growthRate.toFixed(1)}% this period!`);
    } else if (data.growthRate > 0) {
      insights.push(`🌱 Positive growth trend. Keep up the good work!`);
    } else if (data.growthRate < -10) {
      insights.push(`⚠️ Declining views. Consider refreshing content or improving SEO.`);
    }

    // Category insights
    if (data.categoryPerformance && data.categoryPerformance.length > 0) {
      const topCategory = data.categoryPerformance[0];
      if (topCategory.views > 0) {
        insights.push(`🏆 "${topCategory.category}" is your top-performing category with ${topCategory.views} views.`);
      }
    }

    return insights.length > 0 ? insights : ['📊 Keep creating great content to see more insights!'];
  }

  // ============================================================================
  // END NEW DASHBOARD METHODS
  // ============================================================================

  /**
   * Get recipe view statistics for private recipes with assigned users
   * Optimized for 75ms response time with comprehensive user information
=======
   * Get recipe view statistics for specific recipe - Organization filtered
>>>>>>> cb6f5425f848038b7a9ee3bcb06d990ab28c47b2
   */
  async getRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined
  ): Promise<any> {
    try {
<<<<<<< HEAD
      // Ultra-optimized single query with all required user information and proper indexing
      const optimizedQuery = `
        SELECT
          r.id,
          r.has_recipe_private_visibility,
          COUNT(DISTINCT ru.user_id) as assigned_users_count,
          u.id as user_id,
          CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')) as user_full_name,
          u.user_email,
          IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)), '') AS user_avatar_link,
          COALESCE(b.branch_name, '') as user_branch,
          COALESCE(d.department_name, '') as user_department,
          MAX(ra.created_at) as last_recipe_view,
          COUNT(ra.id) as total_view_count
        FROM mo_recipe r
        INNER JOIN mo_recipe_user ru ON r.id = ru.recipe_id AND ru.status = 'active'
        INNER JOIN nv_users u ON ru.user_id = u.id AND u.user_status NOT IN ('cancelled', 'deleted')
        LEFT JOIN nv_branches b ON u.branch_id = b.id
        LEFT JOIN nv_departments d ON u.department_id = d.id
        LEFT JOIN mo_recipe_analytics ra ON ra.entity_id = r.id
          AND ra.entity_type = 'recipe'
          AND ra.event_type = 'recipe_view'
          AND ra.user_id = u.id
        WHERE r.id = :recipeId
          AND r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
        GROUP BY r.id, r.has_recipe_private_visibility, u.id, u.user_first_name, u.user_last_name,
                 u.user_email, u.user_avatar, b.branch_name, d.department_name
        HAVING assigned_users_count > 0
        ORDER BY total_view_count DESC, user_full_name ASC
      `;

      const result = await sequelize.query(optimizedQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      if (!result.length) {
        return {
          status: false,
          message: "Recipe not found, access denied, or no assigned users",
        };
      }

      const firstRow = result[0] as any;

      // Check if recipe is private
      if (!firstRow.has_recipe_private_visibility) {
        return {
          status: false,
          message: "Recipe view statistics are only available for private recipes",
        };
      }

      // Format response with optimized data structure
      const userData = result.map((row: any) => ({
        id: row.user_id,
        user_full_name: row.user_full_name.trim() || 'Unknown User',
        user_email: row.user_email || '',
        user_avatar: row.user_avatar_link || '',
        user_branch: row.user_branch || '',
        user_department: row.user_department || '',
        last_recipe_view: row.last_recipe_view || '',
        total_view_count: parseInt(row.total_view_count) || 0,
      }));

      return {
        status: true,
        message: "Recipe view statistics retrieved successfully",
        data: userData,
=======
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id,
           r.recipe_name,
           COUNT(a.id) as total_views,
           COUNT(DISTINCT a.user_id) as unique_viewers,
           MAX(a.created_at) as last_viewed,
           MIN(a.created_at) as first_viewed
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id AND a.event_type = 'recipe_view'
         WHERE r.id = :recipeId
         ${orgFilter}
         GROUP BY r.id, r.recipe_name`,
        {
          replacements: { recipeId, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        status: true,
        data: result[0] || {
          id: recipeId,
          recipe_name: null,
          total_views: 0,
          unique_viewers: 0,
          last_viewed: null,
          first_viewed: null,
        },
>>>>>>> cb6f5425f848038b7a9ee3bcb06d990ab28c47b2
      };
    } catch {
      return {
        status: false,
        data: {
          id: recipeId,
          recipe_name: null,
          total_views: 0,
          unique_viewers: 0,
          last_viewed: null,
          first_viewed: null,
        },
      };
    }
  }

  /**
   * Reset recipe view statistics for specific recipe - Organization filtered
   */
  async resetRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined,
    userIds?: number[] | string
  ): Promise<any> {
    try {
<<<<<<< HEAD
      // First, verify the recipe exists and is private with assigned users
      const recipeCheckQuery = `
        SELECT
          r.id,
          r.recipe_title,
          r.has_recipe_private_visibility,
          r.organization_id,
          COUNT(ru.user_id) as assigned_users_count
        FROM mo_recipe r
        LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
        WHERE r.id = ?
          AND r.recipe_status != ?
          ${organizationId ? "AND r.organization_id = ?" : ""}
        GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.organization_id
      `;

      const replacements: (number | string)[] = [recipeId, 'deleted'];
      if (organizationId) {
        replacements.push(organizationId);
      }

      const recipeResult = await sequelize.query(recipeCheckQuery, {
        replacements,
        type: QueryTypes.SELECT,
      });

      if (!recipeResult.length) {
        return {
          status: false,
          message: "Recipe not found or access denied",
        };
      }

      const recipe = recipeResult[0] as any;

      // Check if recipe is private
      if (!recipe.has_recipe_private_visibility) {
        return {
          status: false,
          message: "Recipe view statistics reset is only available for private recipes",
        };
      }

      // Check if recipe has assigned users
      const assignedUsersCount = parseInt(recipe.assigned_users_count) || 0;
      if (assignedUsersCount === 0) {
        return {
          status: false,
          message: "Recipe must have assigned users to reset statistics",
        };
      }

      if (userIds && userIds.length > 0) {
        // Reset for specific user IDs - only remove statistics for those users
        // Use a single optimized query to delete analytics records for assigned users only

        const destroyObj : any = {
          event_type: 'recipe_view',
          entity_type: 'recipe',
          entity_id: recipeId,
          user_id: userIds
        }

        if(organizationId){
          destroyObj.organization_id = organizationId;
        }

         await  Analytics.destroy({
          where: destroyObj
        });
      }
      return {
        status: true,
        message: "Recipe view statistics reset successfully for specified users"
      };
    } catch (error: any) {

      // Provide more specific error messages
      let errorMessage = "Error resetting recipe view statistics";

      if (error.name === 'SequelizeConnectionError') {
        errorMessage = "Database connection error";
      } else if (error.name === 'SequelizeDatabaseError') {
        errorMessage = "Database query error";
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return {
        status: false,
        message: errorMessage
=======
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      // Build user filter if userIds provided
      let userFilter = "";
      let userIdArray: number[] = [];

      if (userIds) {
        if (Array.isArray(userIds)) {
          userIdArray = userIds;
        } else if (typeof userIds === "string") {
          // Try to parse as JSON array or comma-separated values
          try {
            userIdArray = JSON.parse(userIds);
          } catch {
            userIdArray = userIds
              .split(",")
              .map((id) => parseInt(id.trim()))
              .filter((id) => !isNaN(id));
          }
        }

        if (userIdArray.length > 0) {
          userFilter = `AND user_id IN (${userIdArray.map(() => "?").join(",")})`;
        }
      }

      const whereClause = `WHERE entity_id = :recipeId AND event_type = 'recipe_view' ${orgFilter} ${userFilter}`;

      const deleteResult = await sequelize.query(
        `DELETE FROM mo_recipe_analytics ${whereClause}`,
        {
          replacements: {
            recipeId,
            organizationId,
            ...userIdArray.reduce((acc, userId, index) => {
              acc[index] = userId;
              return acc;
            }, {} as any),
          },
          type: QueryTypes.DELETE,
        }
      );

      return {
        status: true,
        message: `Recipe view statistics reset successfully`,
        deleted_records: Array.isArray(deleteResult) ? deleteResult.length : 0,
      };
    } catch {
      return {
        status: false,
        message: "Failed to reset recipe view statistics",
        deleted_records: 0,
>>>>>>> cb6f5425f848038b7a9ee3bcb06d990ab28c47b2
      };
    }
  }
}

export default new AnalyticsService();
